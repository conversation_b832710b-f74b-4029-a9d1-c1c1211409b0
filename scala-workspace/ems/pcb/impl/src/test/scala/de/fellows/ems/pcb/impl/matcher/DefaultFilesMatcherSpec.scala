package de.fellows.ems.pcb.impl.matcher

import de.fellows.ems.pcb.impl.matcher.DefaultFilesMatcher
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.OptionValues
import org.yaml.snakeyaml.Yaml

import java.io.{File, FileInputStream}
import java.nio.file.{Path, Paths}
import scala.jdk.CollectionConverters._

class DefaultFilesMatcherSpec extends AnyWordSpec with Matchers with OptionValues {

  implicit val serviceDefinition: ServiceDefinition = ServiceDefinition("test-service")

  case class ExpectedFileMatch(
    filename: String,
    expectedFileType: Option[String],
    expectedCategory: Option[String]
  )

  case class TestProject(
    name: String,
    path: Path,
    expectedMatches: Seq[ExpectedFileMatch]
  )

  private def loadExpectedResults(projectPath: Path): Seq[ExpectedFileMatch] = {
    val yamlFile = projectPath.resolve("expected.yaml").toFile
    if (!yamlFile.exists()) {
      fail(s"Expected YAML file not found: ${yamlFile.getAbsolutePath}")
    }

    val yaml = new Yaml()
    val data = yaml.load(new FileInputStream(yamlFile)).asInstanceOf[java.util.Map[String, Any]]
    val files = data.get("files").asInstanceOf[java.util.List[java.util.Map[String, Any]]]

    files.asScala.map { fileData =>
      ExpectedFileMatch(
        filename = fileData.get("filename").asInstanceOf[String],
        expectedFileType = Option(fileData.get("expectedFileType")).map(_.asInstanceOf[String]),
        expectedCategory = Option(fileData.get("expectedCategory")).map(_.asInstanceOf[String])
      )
    }.toSeq
  }

  private def discoverTestProjects(): Seq[TestProject] = {
    val gerberResourcesPath = Paths.get(getClass.getResource("/gerber-assets").toURI)
    
    gerberResourcesPath.toFile.listFiles()
      .filter(_.isDirectory)
      .filter(dir => new File(dir, "expected.yaml").exists())
      .map { projectDir =>
        val projectPath = projectDir.toPath
        TestProject(
          name = projectDir.getName,
          path = projectPath,
          expectedMatches = loadExpectedResults(projectPath)
        )
      }.toSeq
  }

  private def testProject(project: TestProject): Unit = {
    val files = project.path.toFile.listFiles()
      .filter(_.isFile)
      .filter(_.getName != "expected.yaml")
      .map(f => FilePath(f.getAbsolutePath))
      .toSeq

    val matcher = DefaultFilesMatcher(files)
    val results = matcher.matchFileType()

    // Verify all expected files are present
    val expectedFileNames = project.expectedMatches.map(_.filename).sorted
    val actualFileNames = files.map(_.filename).sorted
    expectedFileNames.toSeq should be(actualFileNames.toSeq)

    // Test each file's matching result
    project.expectedMatches.foreach { expected =>
      val testFile = files.find(_.filename == expected.filename)
      testFile should be(defined)

      val actualResult = results.get(testFile.get).flatten
      
      withClue(s"File ${expected.filename} in project ${project.name}:") {
        (actualResult.map(_.fileType), actualResult.flatMap(_.category)) should be(
          (expected.expectedFileType, expected.expectedCategory)
        )
      }
    }
  }

  "DefaultFilesMatcher" should {

    "match files according to YAML expectations for all test projects" in {
      val projects = discoverTestProjects()
      
      if (projects.isEmpty) {
        fail("No test projects found with expected.yaml files. Please add test projects to /gerber-assets/ resources.")
      } else {
        info(s"Found ${projects.length} test projects: ${projects.map(_.name).mkString(", ")}")
        projects.foreach(testProject)
      }
    }
  }

  // Individual test methods for each discovered project will be generated dynamically
  discoverTestProjects().foreach { project =>
    s"DefaultFilesMatcher for project ${project.name}" should {
      s"correctly match all files in ${project.name}" in {
        testProject(project)
      }
    }
  }
}
