package de.fellows.ems.pcb.impl.matcher

import de.fellows.ems.pcb.impl.matcher.DefaultFilesMatcher
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition

import java.io.{File, PrintWriter}

object GenerateExpectedYaml extends App {
  implicit val serviceDefinition: ServiceDefinition = ServiceDefinition("test-service")

  def main(args: Array[String]): Unit = {
    if (args.isEmpty) {
      println("❌ Usage: GenerateExpectedYaml <project-folder-name>")
      println("📁 Available projects:")
      listAvailableProjects()
      return
    }

    val projectName = args(0)
    val assetsPath = "scala-workspace/ems/pcb/impl/src/test/resources/gerber-assets"
    val projectDir = new File(assetsPath, projectName)

    if (!projectDir.exists() || !projectDir.isDirectory) {
      println(s"❌ Project directory not found: ${projectDir.getAbsolutePath}")
      println("📁 Available projects:")
      listAvailableProjects()
      return
    }

    println(s"🔍 Processing project: $projectName")
    println(s"📂 Path: ${projectDir.getAbsolutePath}")
    
    // Check if expected.yaml already exists
    val yamlFile = new File(projectDir, "expected.yaml")
    if (yamlFile.exists()) {
      println(s"⚠️  expected.yaml already exists!")
      println(s"   This will overwrite the existing file.")
      println(s"   Press Enter to continue or Ctrl+C to cancel...")
      scala.io.StdIn.readLine()
    }

    processProject(projectDir)
    println(s"\n🎯 Generation complete for $projectName!")
  }

  def listAvailableProjects(): Unit = {
    val assetsPath = "scala-workspace/ems/pcb/impl/src/test/resources/gerber-assets"
    val assetsDir = new File(assetsPath)
    
    if (assetsDir.exists()) {
      val projects = assetsDir.listFiles().filter(_.isDirectory)
      projects.foreach(p => println(s"   - ${p.getName}"))
    } else {
      println(s"   Assets directory not found: $assetsPath")
    }
  }

  def processProject(projectDir: File): Unit = {
    // Get all files except existing expected.yaml
    val allFiles = projectDir.listFiles()
      .filter(_.isFile)
      .filter(_.getName != "expected.yaml")
      .sortBy(_.getName)

    if (allFiles.isEmpty) {
      println(s"   ⚠️  No files found in ${projectDir.getName}")
      return
    }

    println(s"   📄 Found ${allFiles.length} files")

    // Convert to FilePath objects
    val filePaths = allFiles.map(f => FilePath(f.getAbsolutePath)).toSeq

    try {
      // Run the DefaultFilesMatcher
      val matcher = DefaultFilesMatcher(filePaths)
      val results = matcher.matchFileType()

      println(s"   🔧 Matcher processed ${results.size} files")

      // Generate YAML content
      val yamlContent = generateYaml(allFiles, results)

      // Write to expected.yaml
      val yamlFile = new File(projectDir, "expected.yaml")
      val writer = new PrintWriter(yamlFile)
      try {
        writer.write(yamlContent)
        println(s"   ✅ Generated: ${yamlFile.getName}")
      } finally {
        writer.close()
      }

      // Print summary of results
      printResultsSummary(allFiles, results)

    } catch {
      case e: Exception =>
        println(s"   ❌ Error processing ${projectDir.getName}: ${e.getMessage}")
        e.printStackTrace()
    }
  }

  def generateYaml(files: Array[File], results: Map[FilePath, Option[de.fellows.utils.internal.FileType]]): String = {
    val yamlBuilder = new StringBuilder()
    yamlBuilder.append("files:\n")

    files.foreach { file =>
      val filename = file.getName
      val filePath = FilePath(file.getAbsolutePath)
      val result = results.get(filePath).flatten

      yamlBuilder.append(s"  - filename: \"$filename\"\n")

      result match {
        case Some(fileType) =>
          yamlBuilder.append(s"    expectedFileType: \"${fileType.fileType}\"\n")
          fileType.category match {
            case Some(category) => yamlBuilder.append(s"    expectedCategory: \"$category\"\n")
            case None => yamlBuilder.append(s"    expectedCategory: null\n")
          }
        case None =>
          yamlBuilder.append(s"    expectedFileType: null\n")
          yamlBuilder.append(s"    expectedCategory: null\n")
      }
    }

    yamlBuilder.toString()
  }

  def printResultsSummary(files: Array[File], results: Map[FilePath, Option[de.fellows.utils.internal.FileType]]): Unit = {
    println(s"   📊 Results summary:")

    val matched = results.values.count(_.isDefined)
    val unmatched = results.values.count(_.isEmpty)

    println(s"      ✅ Matched: $matched files")
    println(s"      ❓ Unmatched: $unmatched files")

    // Group by file type
    val byType = results.values.flatten.groupBy(_.fileType).map {
      case (fileType, types) => fileType -> types.size
    }.toSeq.sortBy(-_._2)

    if (byType.nonEmpty) {
      println(s"      📋 File types found:")
      byType.take(5).foreach { case (fileType, count) =>
        println(s"         - $fileType: $count files")
      }
      if (byType.length > 5) {
        println(s"         ... and ${byType.length - 5} more types")
      }
    }

    // Show some example matches
    println(s"      📝 Example matches:")
    results.take(3).foreach { case (filePath, fileTypeOpt) =>
      val filename = filePath.filename
      fileTypeOpt match {
        case Some(fileType) => println(s"         - $filename → ${fileType.fileType} (${fileType.category.getOrElse("no category")})")
        case None => println(s"         - $filename → unmatched")
      }
    }
  }
}
