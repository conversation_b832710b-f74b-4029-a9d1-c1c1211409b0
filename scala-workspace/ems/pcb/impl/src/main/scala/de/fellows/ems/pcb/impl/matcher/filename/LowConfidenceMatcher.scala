package de.fellows.ems.pcb.impl.matcher.filename

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.filecontent.{GerberOption, Keywords, MechanicalOption}
import de.fellows.ems.pcb.impl.matcher.{FileMatch, SingleFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.pcb.model.LayerConstants.Mime
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.{ExcellonFileTypeDetector, GerberFileTypeDetector}
import de.fellows.utils.{FilePath, PathUtils}

class LowConfidenceMatcher(implicit serviceDefinition: ServiceDefinition) extends SingleFileMatcher(false) {
  val id      = "low-confidence-matcher"
  val service = serviceDefinition.name

  val confidence = VeryLowConfidence

  private val metal = "metal-([0-1]+)".r
  private val layer = "(L|layer|art|lay).*?([0-9]+)".r
  private val c     = "c([0-9]+)".r
  private val cu1   = "([0-9]+)(-|_|\\.|\\s)?cu".r
  private val cu2   = "cu(-|_|\\.|\\s)?([0-9]+)".r
  private val _l    = "(-|_|\\.|\\s)l([0-9]+)".r
  private val l2    = "l([0-9]+)(t|i|b)?".r
  private val lay   = ".*lay([0-9]*)(.*)\\.gbr".r
  private val sm    = "sm([0-9]+)".r
  private val pas   = "pas([0-9]+)".r

  private val number = "[0-9]+".r

  private val knownExtensions = Seq(".gbr", ".grb", ".pho", ".gdo", ".gerber", ".ger", ".gbx", ".art", ".spl")
  private val outlineKeywords = Seq("profile", "outline", "dimension", "otln", "outln", "kontur")

  private val outln1 = "bd.*\\.pho$".r
  private val outln2 = "brd.*\\.gbr$".r

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME, ExcellonFileTypeDetector.MIME))

  private def createDrillMatch(): Seq[FileMatch] =
    Seq(createMatch(LayerConstants.DRILL, Mime.gerber, LayerConstants.Categories.mechanical))

  private def looksLikeGerber(f: FilePath): Boolean =
    PathUtils.probeContentType(f.toJavaPath).toOption.flatten.contains(GerberFileTypeDetector.MIME)

  private val defaultGerber = Seq(createMatch(LayerConstants.UNKNOWN))

  override def matchFile(filename: String, f: FilePath, mime: Option[String]): Seq[FileMatch] = {
    val name        = filename.toLowerCase()
    val mayBeGerber = looksLikeGerber(f)
    if (knownExtensions.exists(name.endsWith) || mayBeGerber) {
      if (!name.contains("assembly") && !name.contains("assy")) {
        if (name.contains("copper") || name.contains("cu.gbr") || name.contains("elek")) {
          checkPossibleCopper(name.replace("copper", "").replace("cu.gbr", "").replace("elek", ""))
        } else if (
          name.contains("screen") || name.contains("silk") || name.contains("ckungsdruck") || name.contains("symbol")
        ) {
          handleSilkScreen(name)
        } else if (name.contains("paste")) {
          handlePaste(name)
        } else if (
          name.contains("mask") || name.contains("solder") || name.contains("resist") || name.contains("abdecklack")
        ) {
          handleSolderMask(name)
        } else if (outlineKeywords.exists(name.contains)) {
          Seq(createMatch(LayerConstants.OUTLINE, Mime.gerber, LayerConstants.Categories.mechanical))
        } else if (name.contains("keep") && name.contains("out")) {
          Seq(createMatch(LayerConstants.KEEP_OUT, Mime.gerber, LayerConstants.Categories.mechanical))
        } else if (name.contains("mil") && name.contains("panel")) {
          Seq(createMatch(LayerConstants.MECHANICAL, Mime.gerber, LayerConstants.Categories.mechanical))
        } else if (name.contains("drill") || name.contains("hole")) {
          if (name.contains("drawing")) {
            Seq(createMatch(LayerConstants.UNKNOWN, Mime.gerber, LayerConstants.Categories.mechanical))
          } else {
            createDrillMatch()
          }
        } else if (layer.findFirstIn(name).isDefined) {
          handleLayer(name)
        } else if (metal.findFirstIn(name).isDefined) {
          handleMetal(name)
        } else if (name.contains("mechanical") || name.contains("fiducial")) {
          Seq(createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical))
        } else if (name.contains("etch") || name.contains("route")) {
          checkPossibleCopper(name)
        } else if (name.contains("pmb")) {
          Seq(createMatch(LayerConstants.PASTE_BOTTOM))
        } else if (name.contains("pmt")) {
          Seq(createMatch(LayerConstants.PASTE_TOP))
        } else if (name.contains("adt")) {
          Seq(createMatch(LayerConstants.ADHESIVE_TOP))
        } else if (name.contains("adb")) {
          Seq(createMatch(LayerConstants.ADHESIVE_BOTTOM))
        } else if (name.contains("smb") || name.contains("sm2")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
        } else if (name.contains("iso")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
        } else if (name.contains("smt")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
        } else if (name.contains("loet")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
        } else if (name.contains("stt")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
        } else if (name.endsWith("stb")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
        } else if (name.contains("slk") || name.contains("sst") || name.contains("ss1")) {
          handleSilkScreen(name)
        } else if (name.contains("bsk") || name.contains("ssb") || name.contains("ss2")) {
          Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
        } else if (name.contains("legend_top")) {
          Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
        } else if (name.contains("legend_bot")) {
          Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
        } else if (name.matches(".*l(o|oe)t.*?stop(p)?.*")) {
          handleSolderMask(name)
        } else if (
          name.contains("gnd") || name.contains("vcc") || name.contains("vdd") || name.contains(
            "vee"
          ) || name.contains("inner") || name.contains("intern")
        ) {
          // last resort for mid layers
          val idx1r = "(gnd|vcc|vee|vdd|inner|intern)(-|_|\\.|\\s)*([0-9]+)".r

          val idx1 = idx1r.findFirstMatchIn(name).map(_.group(3).toInt)
          val idx2 = _l.findFirstMatchIn(name).map(_.group(2).toInt)
          Seq(createMatch(LayerConstants.COPPER_MID, index = idx1.orElse(idx2)))
        } else if (cu1.findFirstIn(name).isDefined) {
          val mtch = cu1.findFirstMatchIn(name).get
          try {
            val idx = BigDecimal(mtch.group(1))
            Seq(createMatch(LayerConstants.COPPER_MID).copy(index = Some(idx.intValue)))
          } catch {
            case e: NumberFormatException => Seq()
          }
        } else if (cu2.findFirstIn(name).isDefined) {
          val mtch = cu2.findFirstMatchIn(name).get
          try {
            val idx = BigDecimal(mtch.group(2))
            Seq(createMatch(LayerConstants.COPPER_MID).copy(index = Some(idx.intValue)))
          } catch {
            case e: NumberFormatException => Seq()
          }
        } else if (l2.findFirstIn(name).isDefined) {
          val mtch = l2.findFirstMatchIn(name).get

          try {
            val idx = BigDecimal(mtch.group(1))
            val tp = mtch.group(2) match {
              case "t" => LayerConstants.COPPER_TOP
              case "i" => LayerConstants.COPPER_MID
              case "b" => LayerConstants.COPPER_BOTTOM
              case _   => LayerConstants.COPPER_MID
            }

            Seq(createMatch(tp).copy(index = Some(idx.intValue)))
          } catch {
            case e: NumberFormatException => Seq()
          }

        } else if (lay.findFirstMatchIn(name).isDefined) {
          lay.findFirstMatchIn(name).map { m =>
            val idx    = m.group(1).toIntOption
            val detail = Option(m.group(2)).getOrElse("")

            if (detail.contains("res")) {
              if (idx.forall(_ <= 2)) {
                Seq(createMatch(LayerConstants.SOLDERMASK_TOP).copy(index = idx))
              } else {
                Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM).copy(index = idx))
              }
            } else {
              Seq(createMatch(LayerConstants.COPPER_MID).copy(index = idx))
            }
          }.get
        } else if (outln1.matches(name) || outln2.matches(name)) {
          Seq(createMatch(LayerConstants.OUTLINE, Mime.gerber, LayerConstants.Categories.mechanical))
        } else {
          // Catch specific cases that may occur
          if (mayBeGerber) {
            handleSpecificCases(name)
          } else {
            Seq()
          }
        }
      } else {
        Seq(createMatch(LayerConstants.MECHANICAL, Mime.gerber, LayerConstants.Categories.mechanical))
      }
    } else if (name.endsWith(".txt")) {
      if (name.contains("nc_param")) {
        Seq(createMatch(
          LayerConstants.DRILL_PARAMETERS,
          cat = LayerConstants.Categories.mechanical,
          mime = Some("text/drillparams")
        ))
      } else {
        Seq()
      }
    } else {
      Seq()
    }
  }

  private def handleSpecificCases(name: String): Seq[FileMatch] =
    if (name.matches("comp\\.([a-z]{3,5})$")) {
      Seq(createMatch(LayerConstants.COPPER_TOP))
    } else if (name.matches("brd\\.([a-z]{3,5})$")) {
      Seq(createMatch(LayerConstants.OUTLINE, Mime.gerber, LayerConstants.Categories.mechanical))
    } else if (name.matches("bd\\.([a-z]{3,5})$")) {
      Seq(createMatch(LayerConstants.OUTLINE, Mime.gerber, LayerConstants.Categories.mechanical))
    } else if (name.matches("sold\\.([a-z]{3,5})$")) {
      Seq(createMatch(LayerConstants.COPPER_BOTTOM))
    } else if (name.endsWith("bo.pho")) {
      Seq(createMatch(LayerConstants.OUTLINE, Mime.gerber, LayerConstants.Categories.mechanical))
    } else if (name.endsWith(".drd")) {
      Seq(createMatch(LayerConstants.DRILL, Mime.gerber, LayerConstants.Categories.mechanical))
    } else if (name.contains("milling")) {
      Seq(createMatch(LayerConstants.DRILL, Mime.gerber, LayerConstants.Categories.mechanical))
    } else if (sm.findFirstMatchIn(name).isDefined) {
      sm
        .findFirstMatchIn(name)
        .flatMap(_.group(1).toIntOption)
        .map { idx =>
          if (idx <= 1) {
            Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
          } else {
            Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
          }
        }
        .getOrElse(defaultGerber)
    } else if (pas.findFirstMatchIn(name).isDefined) {
      pas
        .findFirstMatchIn(name)
        .flatMap(_.group(1).toIntOption)
        .map { idx =>
          if (idx <= 1) {
            Seq(createMatch(LayerConstants.PASTE_TOP))
          } else {
            Seq(createMatch(LayerConstants.PASTE_BOTTOM))
          }
        }
        .getOrElse(defaultGerber)
    } else if (name.contains("pos1.")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
    } else if (name.contains("pos2.")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
    } else if (name.contains("lstop")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
    } else if (name.contains("lsbot")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
    } else if (name.contains("stop")) {
      if (name.contains("bstop")) {
        Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
      } else if (name.contains("tstop")) {
        Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
      } else if (name.contains("bot")) {
        Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
      } else if (name.contains("top")) {
        Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
      } else {
        Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
      }
    } else if (name.contains("cm.")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
    } else if (name.contains("sm.")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
    } else if (name.contains("dim.") || name.contains("board.")) {
      Seq(createMatch(LayerConstants.OUTLINE, Mime.gerber, LayerConstants.Categories.mechanical))
    } else {
      Keywords.getMatchOption(name).flatMap(_.headOption) match {
        case Some(value) =>
          Seq(createMatch(
            ltype = value.fileType,
            cat = value match {
              case _: MechanicalOption => LayerConstants.Categories.mechanical
              case _: GerberOption     => LayerConstants.Categories.gerber
            },
            conf = value.confidenceModifier.map(m => confidence + m)
          ))

        case None =>
          if (name.contains("top")) {
            Seq(createMatch(LayerConstants.COPPER_TOP))
          } else if (name.contains("bot")) {
            Seq(createMatch(LayerConstants.COPPER_BOTTOM))
          } else {
            defaultGerber
          }
      }

    }

  private def handleMetal(name: String) =
    metal.findFirstMatchIn(name) match {
      case Some(m) =>
        try {
          val idx = BigDecimal(m.group(1))
          Seq(createMatch(LayerConstants.COPPER_MID).copy(index = Some(idx.intValue)))
        } catch {
          case e: NumberFormatException => Seq(createMatch(LayerConstants.COPPER_MID))
        }
      case None => defaultGerber
    }

  private def handleLayer(name: String) =
    layer.findFirstMatchIn(name) match {
      case Some(m) =>
        try {
          val idx = BigDecimal(m.group(2))
          val thisConfidence =
            if (name.contains("layer")) {
              MediumHighConfidence
            } else if (name.contains("lay")) {
              MediumLowConfidence
            } else {
              confidence
            }

          val copper =
            if (name.contains("top")) {
              LayerConstants.COPPER_TOP
            } else if (name.contains("bot")) {
              LayerConstants.COPPER_BOTTOM
            } else {
              LayerConstants.COPPER_MID
            }
          Seq(createMatch(copper, conf = Some(thisConfidence)).copy(
            index = Some(idx.intValue)
          ))

        } catch {
          case e: NumberFormatException => Seq(createMatch(LayerConstants.COPPER_MID))
        }
      case None => defaultGerber
    }

  private def handleSolderMask(name: String) =
    if (name.contains("top") || name.contains("oben") || name.contains("ober")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
    } else if (name.contains("bottom") || name.contains("bot") || name.contains("unten") || name.contains("unter")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
    } else if (name.contains("-01") || name.contains("-1")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
    } else if (name.contains("-02") || name.contains("-2")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
    } else if (name.matches("^.*(solder|mask|resist).*1.*$")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
    } else if (name.matches("^.*(solder|mask|resist).*2.*$")) {
      Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
    } else {
      // catch specific cases
      if (name.contains("mask_t")) {
        Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
      } else if (name.contains("mask_b")) {
        Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
      } else {
        val cleanName = name
          .replace("maks", "")
          .replace("solder", "")
          .replace(".gbr", "")
          .replace(".grb", "")
        if (cleanName.contains("t")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
        } else if (cleanName.contains("b")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_BOTTOM))
        } else if (name.contains("mask.")) {
          Seq(createMatch(LayerConstants.SOLDERMASK_TOP))
        } else {
          defaultGerber
        }
      }
    }

  private def handlePaste(name: String) =
    if (name.contains("top") || name.contains("oben") || name.contains("ober")) {
      Seq(createMatch(LayerConstants.PASTE_TOP))
    } else if (name.contains("bottom") || name.contains("bot") || name.contains("unten") || name.contains("unter")) {
      Seq(createMatch(LayerConstants.PASTE_BOTTOM))
    } else if (lay.findFirstMatchIn(name).isDefined) {
      val idx = lay.findFirstMatchIn(name).flatMap(_.group(1).toIntOption)
      if (idx.forall(_ <= 2)) {
        Seq(createMatch(LayerConstants.PASTE_TOP))
      } else {
        Seq(createMatch(LayerConstants.PASTE_BOTTOM))
      }
    } else if (name.replace("paste", "").contains("t")) {
      Seq(createMatch(LayerConstants.PASTE_TOP))
    } else if (name.replace("paste", "").contains("b")) {
      Seq(createMatch(LayerConstants.PASTE_BOTTOM))
    } else {
      Seq(createMatch(LayerConstants.PASTE_TOP))
    }

  private def handleSilkScreen(name: String) = {
    val slkWithLayer = "slk(-|_|\\.|\\s)*([0-9]+)".r
    if (name.contains("top") || name.contains("oben") || name.contains("ober")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
    } else if (name.contains("bottom") || name.contains("bot") || name.contains("unten") || name.contains("unter")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
    } else if (lay.findFirstMatchIn(name).isDefined) {
      val idx = lay.findFirstMatchIn(name).flatMap(_.group(1).toIntOption)
      if (idx.forall(_ <= 2)) {
        Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
      } else {
        Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
      }
    } else if (name.contains("-01") || name.contains("-1")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
    } else if (name.contains("-02") || name.contains("-2")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
    } else if (slkWithLayer.matches(name)) {
      slkWithLayer.findFirstMatchIn(name).map { mtch =>
        val idx = mtch.group(2).toInt
        if (idx <= 1) {
          Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
        } else {
          Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
        }
      }.getOrElse {
        Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
      }
    } else if (name.matches("^.*(silk|screen).*1.*$")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
    } else if (name.matches("^.*(silk|screen).*2.*$")) {
      Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
    } else {
      // catch specific cases
      if (name.matches("^.*silk[-_ ]t.*$") || name.matches("^.*screen[-_ ]t.*$")) {
        Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
      } else if (name.matches("^.*silk[-_ ]b.*$") || name.matches("^.*screen[-_ ]b.*$")) {
        Seq(createMatch(LayerConstants.SILKSCREEN_BOTTOM))
      } else if (name.contains("silk.")) {
        Seq(createMatch(LayerConstants.SILKSCREEN_TOP))
      } else {
        defaultGerber
      }
    }
  }

  private def checkPossibleCopper(name: String) =
    if (name.contains("top") || name.contains("oben") || name.contains("ober")) {
      Seq(createMatch(LayerConstants.COPPER_TOP))
    } else if (name.contains("bottom") || name.contains("bot") || name.contains("unten") || name.contains("unter")) {
      Seq(createMatch(LayerConstants.COPPER_BOTTOM))
    } else {
      val idx = "([0-9]+)".r
      idx.findFirstMatchIn(name) match {
        case Some(value) =>
          val layer = value.group(1).toInt
          Seq(createMatch(LayerConstants.COPPER_MID, index = Some(layer)))
        case None => Seq(createMatch(LayerConstants.COPPER_MID))
      }

    }
}
